import { RiCloseLine } from "@remixicon/react";
import Highlight from "@tiptap/extension-highlight";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import { generateHTML } from "@tiptap/html";
import StarterKit from "@tiptap/starter-kit";
import ContentLock from "components/Common/CustomTiptapEditor/extensions/ContentLock";
import GraphPlaceholder from "components/Common/CustomTiptapEditor/extensions/GraphPlaceholder";
import useReportStore from "features/Reports/store/report";
import { Button, Modal } from "react-bootstrap";
import "./styles.scss";
import Image from "@tiptap/extension-image";
import useCheckReportSection from "../hooks/useCheckReportSection";

const extensions = [
  StarterKit,
  Highlight,
  Underline,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,
  GraphPlaceholder,
  ContentLock,
  TextAlign.configure({
    types: ["heading", "paragraph"],
  }),
  Image,
];

const getContentHtml = (content: any): string => {
  if (!content) return "<p><em>No content added yet.</em></p>";

  if (
    typeof content === "object" &&
    content !== null &&
    content.type === "html"
  ) {
    return content.content || "<p><em>No content added yet.</em></p>";
  }

  if (typeof content === "string") {
    try {
      const parsedContent = JSON.parse(content);
      return generateHTML(parsedContent, extensions);
    } catch (error) {
      return content || "<p><em>No content added yet.</em></p>";
    }
  }

  if (typeof content === "object" && content !== null) {
    try {
      return generateHTML(content, extensions);
    } catch (error) {
      return "<p><em>Error displaying content.</em></p>";
    }
  }

  return "<p><em>No content added yet.</em></p>";
};

interface ReportPreviewProps {
  show: boolean;
  onClose: () => void;
}

const ReportPreview = ({ show, onClose }: ReportPreviewProps) => {
  const reportInfo = useReportStore((state) => state.reportInfo);
  const { isGenerateSection } = useCheckReportSection();
  

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      size="lg"
      centered
      className="report-preview-modal"
    >
      <Modal.Header className="border-0 pb-0">
        <Modal.Title className="w-100 text-center">
          <h3 className="mb-0 fw-bold">Report Preview</h3>
        </Modal.Title>
        <Button
          variant="link"
          className="text-decoration-none position-absolute end-0 top-0 mt-2 me-2 text-dark"
          onClick={handleClose}
        >
          <RiCloseLine size={24} />
        </Button>
      </Modal.Header>

      <Modal.Body>
        <div className="report-preview-content">
          {reportInfo.title && (
            <div className="preview-title mb-4 text-center">
              <h1 className="fw-bold">{reportInfo.title}</h1>
              <hr />
            </div>
          )}

          <div className="preview-sections">
            {reportInfo.sections.map((block, index) => (
              <div key={index} className="preview-section mb-4 overflow-auto">
                <h4 className="section-title fw-bold text-secondary mb-3">
                  {block.title}
                </h4>
                <div
                  className="section-content w-100"
                  dangerouslySetInnerHTML={{
                    __html: getContentHtml(block.content),
                  }}
                />
                {/* {index < reportInfo.sections.length - 1 && (
                  <hr className="my-4" />
                )} */}
              </div>
            ))}
          </div>

          {reportInfo.sections.length === 0 && (
            <div className="text-center text-muted py-5">
              <p className="mb-0">No sections added to the report yet.</p>
            </div>
          )}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ReportPreview;
